[package]
name = "gdrive-stealth-sync"
version = "0.1.0"
edition = "2021"

[dependencies]
# For running as a Windows Service
windows-service = "0.6"

# For the core async runtime
tokio = { version = "1", features = ["full"] }

# For watching the file system for new files
notify = "6.1"
notify-debouncer-full = "0.3.1"

# For handling JSON config
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# For Google Drive API and authentication - updated to compatible versions
google-drive3 = "6.0.0"
yup-oauth2 = "12.1.0"
hyper = "1.0"
hyper-util = { version = "0.1", features = ["client-legacy", "http1"] }
hyper-rustls = { version = "0.27", features = ["native-tokio", "http1", "tls12", "logging", "ring"], default-features = false }

# For logging to the Windows Event Log
log = "0.4"
eventlog = "0.3.0"